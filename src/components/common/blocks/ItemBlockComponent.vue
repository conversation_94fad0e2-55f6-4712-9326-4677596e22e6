<template>
  <q-card class="q-pa-lg q-ma-md item-block-container" @click="handleCardClick">
    <!-- Header with three-dot menu and save indicator -->
    <div class="item-top-bar">
      <div class="row items-center q-gutter-sm">
        <q-btn flat round color="grey-6" size="sm" class="three-dot-menu" @click.stop="toggleMenu">
          <ThreeDots size="xs" color="grey-6" />
          <q-menu v-model="showMenu" anchor="bottom middle" self="top middle">
            <q-list style="min-width: 150px">
              <q-item clickable v-close-popup @click="onDuplicate">
                <q-item-section avatar>
                  <q-icon name="content_copy" />
                </q-item-section>
                <q-item-section>Duplicate</q-item-section>
              </q-item>
              <q-item clickable v-close-popup @click="onDelete">
                <q-item-section avatar>
                  <q-icon name="delete" />
                </q-item-section>
                <q-item-section>Delete</q-item-section>
              </q-item>
            </q-list>
          </q-menu>
        </q-btn>
      </div>
    </div>

    <!-- โหมดปกติ -->
    <template v-if="!showAnswerSettings">
      <div class="row">
        <div class="col">
          <UploadImage
            v-if="isImageUploadAllowed"
            v-model="showImageDialog"
            :item-block-id="itemBlock.id"
            :question-id="firstQuestionId"
            @image-uploaded="handleImageUploaded"
          />
          <div v-if="showQuestionNumber" class="text-subtitle2 text-primary q-mb-sm">
            ข้อที่ {{ questionNumber }}
          </div>
          <EditorTool
            :key="`question-${itemBlock.id}-${itemBlock.questions?.[0]?.id || 'new'}`"
            label="พิมพ์คำถาม..."
            :initialValue="currentQuestionText"
            v-model:content="headerText"
            @blur="handleQuestionBlur"
            @update:content="updateQuestionText"
          />

          <div v-if="uploadedImagePath" class="q-mt-md" style="position: relative">
            <img :src="uploadedImagePath" :style="imageStyle" alt="Uploaded" @load="onImageLoad" />
            <FloatImageBtn
              v-if="imageNaturalWidth && imageNaturalHeight"
              :item-block="itemBlock"
              :original-width="imageNaturalWidth"
              :original-height="imageNaturalHeight"
              :current-width="imageWidth"
              :current-height="imageHeight"
              @resize="onImageResize"
              style="position: absolute; top: 8px; right: 8px; z-index: 2"
            />
          </div>
        </div>
        <div class="col-1 q-ml-sm">
          <q-btn
            v-if="isImageUploadAllowed"
            flat
            round
            icon="image"
            color="grey"
            class="bg-transparent"
            padding="sm"
            @click="onImageBtnClick"
          />
        </div>
        <div class="col-auto">
          <q-select
            v-model="selectedBlockBody"
            :options="filteredBlockBodyOptions"
            filled
            dense
            style="min-width: 200px"
            color="accent"
            option-label="label"
            map-options
            @update:model-value="onBlockBodyChange"
          >
            <template v-slot:option="scope">
              <q-item v-bind="scope.itemProps">
                <q-item-section avatar>
                  <q-icon :name="scope.opt.icon" />
                </q-item-section>
                <q-item-section>
                  <q-item-label>{{ scope.opt.label }}</q-item-label>
                </q-item-section>
              </q-item>
            </template>
            <template v-slot:selected>
              <div class="row items-center">
                <q-icon :name="selectedBlockBody?.icon" class="q-mr-sm" />
                <div>{{ selectedBlockBody?.label }}</div>
              </div>
            </template>
          </q-select>
        </div>
      </div>

      <component
        :key="`${props.itemBlock.id}-${selectedBlockBody.value}`"
        :is="currentComponent"
        :item-block="itemBlock"
        :type="props.type"
        class="q-mb-md q-pt-md q-ml-md"
        @option:created="handleOptionCreated"
        @option:updated="handleOptionUpdatedWithRefresh"
        @update:question="handleGridQuestionUpdate"
        @update:option="handleGridOptionUpdate"
      />

      <q-separator inset color="#898989" />

      <div class="row items-center justify-between q-mt-md no-wrap">
        <div class="col-auto">
          <div v-if="props.type === 'quiz'" class="row items-center q-gutter-sm">
            <q-btn
              class="text-accent"
              icon="event_available"
              label="เฉลยคำตอบ"
              @click="toggleAnswerSettings"
            />
            <div class="text-caption text-grey-7">({{ number }} point)</div>
          </div>
        </div>
        <div class="col-auto">
          <ItemBlockFooter
            label="จำเป็น"
            :is-required="itemBlock.isRequired ?? false"
            @duplicate="onClickDuplicateItem"
            @delete="onClickDeleteItem"
            @update:is-required="handleIsRequiredToggle"
            @click="$emit('focus-fab')"
          />
        </div>
      </div>
    </template>

    <template v-else>
      <div class="row items-center text-h6 q-mb-md text-black">
        <q-icon name="event_available" class="q-mr-sm" size="md" />
        <div>เลือกคำตอบที่ถูกต้อง</div>
      </div>

      <div class="q-mb-md q-pl-sm">
        <div class="row items-center q-gutter-sm">
          <!-- ข้อความคำถาม -->
          <div class="text-subtitle1 text-weight-medium text-black">
            {{ headerText || 'คำถาม' }}
          </div>

          <q-space />
          <div class="text-subtitle1 text-weight-medium text-black">คะแนน</div>
          <q-input
            v-if="props.type === 'quiz'"
            v-model.number="number"
            type="number"
            filled
            :min="0"
            :max="100"
            step="1"
            style="max-width: 100px"
            dense
          />
        </div>
      </div>

      <div class="q-gutter-sm q-ml-sm q-mb-md">
        <div
          v-for="(choice, index) in localOptions"
          :key="index"
          class="row items-center q-pa-sm rounded-borders cursor-pointer"
          :class="{
            'bg-green-1 text-green-10': isSelected(choice.id),
            'bg-grey-2': !isSelected(choice.id),
          }"
          @click="onSelect(choice.id)"
        >
          <!-- <q-radio
            v-model="selectedAnswer"
            :val="choice.id"
            size="sm"
            color="green"
            class="q-mr-sm"
          /> -->
          <q-radio
            v-if="selectedBlockBody?.value === 'RADIO'"
            v-model="selectedAnswer"
            :val="choice.id"
            color="green"
          />
          <q-checkbox
            v-else-if="selectedBlockBody?.value === 'CHECKBOX'"
            v-model="selectedAnswers"
            :val="choice.id"
            color="green"
            class="q-mr-sm"
          />

          <div class="text-body1">{{ choice.optionText }}</div>

          <!-- ไอคอนถูก -->
          <q-space />
          <q-icon v-if="selectedAnswer === choice.id" name="check" color="green" size="sm" />
        </div>
      </div>
      <!-- ปุ่ม -->
      <div class="row items-center q-mt-md">
        <div class="col-auto">
          <q-btn
            v-if="props.type === 'quiz'"
            class="text-accent"
            icon="event_available"
            label="เพิ่มคำตอบข้อเสนอแนะ"
          />
        </div>
        <q-space />
        <div class="col-auto">
          <q-btn color="accent" flat label="เสร็จสิ้น" @click="handleFinish" />
        </div>
      </div>
    </template>
  </q-card>
</template>

<script setup lang="ts">
import { computed, ref, provide, watch, onMounted } from 'vue';
import type { Component } from 'vue';
import type { DropdownItemBlockType, ItemBlock, ItemBlockType, Option } from 'src/types/models';
import ItemBlockFooter from 'src/components/common/blocks/ItemBlockFooter.vue';
import EditorTool from 'src/components/common/EditorTool.vue';
import ThreeDots from 'src/components/common/ThreeDots.vue';
import type { BlockBodyOptionsType } from 'src/types/app';
import OptionBody from './OptionBody.vue';
import TextBody from './TextBody.vue';
import CheckBoxBody from './CheckBoxBody.vue';
import GridBody from './GridBody.vue';
import FileUploadBody from './FileUploadBody.vue';
import { blockBodyOptions } from 'src/data/blocks';
import { extractBlockBodyType } from 'src/utils/block_helper';
import { AssessmentService } from 'src/services/asm/assessmentService';
import { useGlobalStore } from 'src/stores/global';
import UploadImage from 'src/components/common/UploadImage.vue';
import FloatImageBtn from 'src/components/common/FloatImageBtn.vue';

import { useRoute } from 'vue-router';

const route = useRoute();
const showQuestionNumber = computed(() => route.path.includes('quiz'));

const number = ref<number>(0);
const selectedAnswer = ref<string | number | null>(null);
const selectedAnswers = ref<(string | number)[]>([]);
const emit = defineEmits([
  'focus-fab',
  'duplicate',
  'delete',
  'update:question',
  'update:type',
  'update:isRequired',
  'update:option',
  'refresh-assessment',
]);
const showAnswerSettings = ref(false);

function handleFinish() {
  showAnswerSettings.value = false;

  // แจ้ง parent ให้ update เฉพาะ option ของ block นี้
  emit('update:option', {
    action: 'refresh-option',
    itemBlockId: props.itemBlock.id,
  });

  // ❌ ไม่ต้อง emit('refresh-assessment', ...)
}
const showImageDialog = ref(false);
const uploadedImagePath = ref<string | null>(null);
const firstQuestionId = computed(() => props.itemBlock.questions?.[0]?.id ?? null);

// Image dimension reactive variables - declared early to avoid initialization errors
const imageWidth = ref<number>(0);
const imageHeight = ref<number>(0);
const imageNaturalWidth = ref<number>(0);
const imageNaturalHeight = ref<number>(0);

function toggleAnswerSettings() {
  showAnswerSettings.value = !showAnswerSettings.value;
}
const props = defineProps<{
  itemBlock: ItemBlock;
  type: 'quiz' | 'evaluate';
  questionNumber?: number;
}>();
watch(
  () => props.itemBlock.questions?.[0]?.score,
  (score) => {
    if (typeof score === 'number') {
      number.value = score;
    }
  },
  { immediate: true },
);

// Create a reactive reference to itemBlock for template usage
const itemBlock = computed(() => props.itemBlock);

// Initialize assessment service and global store
const assessmentService = new AssessmentService(props.type || 'evaluate');
const globalStore = useGlobalStore();

// Save state tracking
const isSaving = ref(false);

// Last saved values for comparison
const lastSavedQuestionText = ref<string>('');

// Loading state for type updates
const isUpdatingType = ref<boolean>(false);

const headerText = ref<string>('');

// Computed property to get the current question text from props
const currentQuestionText = computed(() => {
  if (props.itemBlock && props.itemBlock.type !== 'HEADER') {
    const firstQuestion = props.itemBlock.questions?.[0];
    return firstQuestion?.questionText || '';
  } else if (props.itemBlock && props.itemBlock.headerBody) {
    return props.itemBlock.headerBody.title || '';
  }
  return '';
});

// Menu state
const showMenu = ref(false);

// Initialize header text based on the itemBlock prop (if available)
// For non-header blocks, use the first question's text
if (props.itemBlock && props.itemBlock.type !== 'HEADER') {
  // For non-header blocks, get question text from the first question
  const firstQuestion = props.itemBlock.questions?.[0];
  headerText.value = firstQuestion?.questionText || '';
  lastSavedQuestionText.value = firstQuestion?.questionText || '';
} else if (props.itemBlock && props.itemBlock.headerBody) {
  // For header blocks, use headerBody title
  headerText.value = props.itemBlock.headerBody.title || '';
}

// Computed property to determine if image upload is allowed for this block
const isImageUploadAllowed = computed(() => {
  // Only allow image upload for certain block types (not for 'question' entries)
  // Adjust this logic as needed based on your app's block type definitions
  // For example, allow only for 'IMAGE' or 'UPLOAD' types, or exclude 'question' type
  const disallowedTypes = ['question']; // Add other disallowed types if needed
  // Use lowercase for comparison to be safe
  const blockType = (props.itemBlock.type || '').toLowerCase();
  return !disallowedTypes.includes(blockType);
});

// Perform save operation on blur for questions
async function performQuestionSave(questionId: number, content: string) {
  try {
    if (!questionId) return;

    // Only save if content has changed
    if (content.trim() === lastSavedQuestionText.value) {
      return;
    }

    isSaving.value = true;
    globalStore.startSaveOperation('Saving...');

    // Prepare the update payload for question following headerBody pattern
    const updatePayload = {
      questionText: content,
      itemBlockId: props.itemBlock.id,
    };

    // Call the updateQuestion API using /questions/{id} endpoint
    const updatedQuestion = await assessmentService.updateQuestion(questionId, updatePayload);

    if (updatedQuestion) {
      // Update the last saved value
      lastSavedQuestionText.value = content;

      globalStore.completeSaveOperation(true, 'Saved successfully');

      // Emit the update to parent components so they can update their state
      emit('update:question', {
        questionId: questionId,
        questionText: updatedQuestion.questionText,
        itemBlockId: props.itemBlock.id,
        updatedQuestion: updatedQuestion,
      });
    }
  } catch (error) {
    console.error('Question save failed:', error);
    globalStore.completeSaveOperation(false, 'Saved successfully');
  } finally {
    isSaving.value = false;
  }
}

// Perform save operation on blur for options
// async function performOptionSave(
//   optionId: number,
//   field: 'optionText' | 'value',
//   value: string | number,
// ) {
//   try {
//     if (!optionId) return;

//     isSaving.value = true;
//     globalStore.startSaveOperation('Saving...');

//     // Import OptionService for direct option updates
//     const { OptionService } = await import('src/services/asm/optionService');
//     const optionService = new OptionService();

//     // Prepare the update payload for option
//     const updatePayload = {
//       [field]: value,
//       itemBlockId: props.itemBlock.id,
//     };

//     // Use the silent update method for save (no notifications)
//     await optionService.updateOptionSilent(optionId, updatePayload);

//     globalStore.completeSaveOperation(true, 'Saved successfully');
//   } catch (error) {
//     console.error(`❌ Failed to save option ${optionId} ${field}:`, error);
//     globalStore.completeSaveOperation(false, 'Saved successfully');
//   } finally {
//     isSaving.value = false;
//   }
// }

async function performOptionSave(
  optionId: number,
  field: 'optionText' | 'value',
  value: string | number,
) {
  try {
    if (!optionId) return;

    isSaving.value = true;
    globalStore.startSaveOperation('Saving...');

    const { OptionService } = await import('src/services/asm/optionService');
    const optionService = new OptionService();

    // ✅ ENHANCED OPTION LOOKUP with fallback to block creator store
    let currentOption = props.itemBlock.options?.find((opt) => opt.id === optionId);

    // ✅ FALLBACK: If option not found in props, check the block creator store
    // This handles the case where option was just created and props haven't updated yet
    if (!currentOption) {
      const { useBlockCreatorStore } = await import('src/stores/block_creator');
      const blockCreatorStore = useBlockCreatorStore();

      // Find the block in the store
      const storeBlock = blockCreatorStore.blocks.find((block) => block.id === props.itemBlock.id);
      if (storeBlock?.options) {
        currentOption = storeBlock.options.find((opt) => opt.id === optionId);
        console.log('🔍 [OPTION-SAVE] Found option in block creator store:', {
          optionId,
          found: !!currentOption,
          storeBlockId: storeBlock.id,
          storeOptionsCount: storeBlock.options.length,
        });
      }

      // ✅ SECOND FALLBACK: Check currentAssessment in store
      if (!currentOption && blockCreatorStore.currentAssessment?.itemBlocks) {
        const assessmentBlock = blockCreatorStore.currentAssessment.itemBlocks.find(
          (block) => block.id === props.itemBlock.id,
        );
        if (assessmentBlock?.options) {
          currentOption = assessmentBlock.options.find((opt) => opt.id === optionId);
          console.log('🔍 [OPTION-SAVE] Found option in current assessment:', {
            optionId,
            found: !!currentOption,
            assessmentBlockId: assessmentBlock.id,
            assessmentOptionsCount: assessmentBlock.options.length,
          });
        }
      }
    }

    if (!currentOption) {
      console.error('❌ [OPTION-SAVE] Option not found in any location:', {
        optionId,
        itemBlockId: props.itemBlock.id,
        propsOptionsCount: props.itemBlock.options?.length || 0,
        propsOptions: props.itemBlock.options?.map((opt) => ({ id: opt.id, text: opt.optionText })),
      });
      throw new Error(`Option with ID ${optionId} not found`);
    }

    // ✅ FIX: Only include fields that are being updated or are required
    // Exclude imagePath from payload when updating text/value to prevent overwriting with transformed URLs
    const updatePayload = {
      itemBlockId: props.itemBlock.id,
      optionText: field === 'optionText' ? String(value) : currentOption.optionText,
      value: field === 'value' ? Number(value) : currentOption.value,
      // ✅ Preserve other optional fields if they exist
      ...(currentOption.sequence !== undefined && { sequence: currentOption.sequence }),
      ...(currentOption.nextSection !== undefined &&
        currentOption.nextSection !== null && { nextSection: currentOption.nextSection }),
    };

    // ✅ CRITICAL FIX: Do NOT include imagePath in the payload for text/value updates
    // This prevents sending transformed URLs back to the backend
    // The backend will preserve the existing imagePath when it's not provided in the update

    console.log('🔍 [OPTION-SAVE] Updating option without imagePath to preserve original format:', {
      optionId,
      field,
      value,
      originalImagePath: currentOption.imagePath,
      updatePayload,
      note: 'imagePath excluded from payload to prevent overwriting with transformed URL',
    });

    await optionService.updateOptionSilent(optionId, updatePayload);
    globalStore.completeSaveOperation(true, 'Saved successfully');
  } catch (error) {
    console.error(`❌ Failed to save option ${optionId} ${field}:`, error);
    globalStore.completeSaveOperation(false, 'Save failed');
  } finally {
    isSaving.value = false;
  }
}

// Perform save operation for question image dimensions
async function performQuestionImageDimensionsSave(
  questionId: number,
  width: number,
  height: number,
) {
  try {
    if (!questionId) return;

    isSaving.value = true;
    globalStore.startSaveOperation('Updating image size...');

    // Prepare the update payload for question image dimensions
    const updatePayload = {
      imageWidth: width,
      imageHeight: height,
      itemBlockId: props.itemBlock.id,
    };

    // Call the updateQuestion API using /questions/{id} endpoint
    const updatedQuestion = await assessmentService.updateQuestion(questionId, updatePayload);

    if (updatedQuestion) {
      globalStore.completeSaveOperation(true, 'Image size updated successfully');

      // Emit the update to parent components so they can update their state
      emit('update:question', {
        questionId: questionId,
        imageWidth: updatedQuestion.imageWidth,
        imageHeight: updatedQuestion.imageHeight,
        itemBlockId: props.itemBlock.id,
        updatedQuestion: updatedQuestion,
        action: 'image-dimensions-updated',
      });
    }
  } catch (error) {
    console.error('Question image dimensions save failed:', error);
    globalStore.completeSaveOperation(false, 'Failed to update image size');
  } finally {
    isSaving.value = false;
  }
}

// Perform save operation for option image upload
async function performOptionImageSave(optionId: number, imagePath: string) {
  try {
    if (!optionId) return;

    isSaving.value = true;
    globalStore.startSaveOperation('Uploading image...');

    // Import OptionService for option image updates
    const { OptionService } = await import('src/services/asm/optionService');
    const optionService = new OptionService();

    // Prepare the update payload for option image - only imagePath and itemBlockId
    const updatePayload = {
      imagePath: imagePath,
      itemBlockId: props.itemBlock.id,
    };

    // Call the updateOptionSilent API using PATCH /options/{id} endpoint
    const updatedOption = await optionService.updateOptionSilent(optionId, updatePayload);

    if (updatedOption) {
      globalStore.completeSaveOperation(true, 'Image uploaded successfully');

      // Emit the update to parent components so they can update their state
      emit('update:option', {
        action: 'image-updated',
        itemBlockId: props.itemBlock.id,
        optionId: optionId,
        imagePath: updatedOption.imagePath,
        updatedOption: updatedOption,
      });
    }
  } catch (error) {
    console.error('Option image save failed:', error);
    globalStore.completeSaveOperation(false, 'Failed to upload image');
  } finally {
    isSaving.value = false;
  }
}

// Handler for question blur event
async function handleQuestionBlur() {
  const content = headerText.value;
  if (props.itemBlock.type !== 'HEADER') {
    const firstQuestion = props.itemBlock.questions?.[0];
    if (firstQuestion && firstQuestion.id) {
      await performQuestionSave(firstQuestion.id, content);
    }
  }
}

// Watch for changes in the computed question text to update local state
watch(
  currentQuestionText,
  (newQuestionText) => {
    if (newQuestionText !== headerText.value) {
      headerText.value = newQuestionText;
      lastSavedQuestionText.value = newQuestionText;
    }
  },
  { immediate: true }, // Ensure it runs immediately on mount
);

// Watch for prop changes to update local state (exactly like HeaderBlock pattern)
watch(
  () => props.itemBlock.questions?.[0]?.questionText,
  (newQuestionText) => {
    if (props.itemBlock.type !== 'HEADER' && newQuestionText !== undefined) {
      headerText.value = newQuestionText;
      lastSavedQuestionText.value = newQuestionText;
    }
  },
  { immediate: true }, // Ensure it runs immediately on mount
);

// Watch for entire itemBlock changes to handle data refresh scenarios
watch(
  () => props.itemBlock,
  (newItemBlock, oldItemBlock) => {
    if (newItemBlock && newItemBlock.type !== 'HEADER') {
      const firstQuestion = newItemBlock.questions?.[0];
      if (firstQuestion?.questionText !== undefined) {
        // Only update if the question text actually changed
        const oldQuestionText = oldItemBlock?.questions?.[0]?.questionText;
        if (firstQuestion.questionText !== oldQuestionText) {
          headerText.value = firstQuestion.questionText;
          lastSavedQuestionText.value = firstQuestion.questionText;
        }
      }

      // Handle image data updates (path and dimensions)
      if (firstQuestion) {
        // Update image path if it changed
        if (firstQuestion.imagePath !== oldItemBlock?.questions?.[0]?.imagePath) {
          uploadedImagePath.value = firstQuestion.imagePath || null;
        }

        // Update image dimensions if they changed
        const oldQuestion = oldItemBlock?.questions?.[0];
        if (
          firstQuestion.imageWidth !== oldQuestion?.imageWidth ||
          firstQuestion.imageHeight !== oldQuestion?.imageHeight
        ) {
          if (firstQuestion.imageWidth && firstQuestion.imageHeight) {
            imageWidth.value = firstQuestion.imageWidth;
            imageHeight.value = firstQuestion.imageHeight;
          }
        }
      }
    } else if (newItemBlock && newItemBlock.headerBody) {
      // For header blocks, use headerBody title
      if (newItemBlock.headerBody.title !== undefined) {
        headerText.value = newItemBlock.headerBody.title;
      }
    }
  },
  { immediate: true, deep: true }, // Deep watch to catch nested changes
);

// Watch for changes in isRequired property to ensure reactivity
watch(
  () => props.itemBlock.isRequired,
  (newIsRequired, oldIsRequired) => {
    // Normalize undefined values to false for comparison
    const normalizedNew = newIsRequired ?? false;
    const normalizedOld = oldIsRequired ?? false;

    // Only log meaningful changes (not undefined → false transitions)
    if (normalizedNew !== normalizedOld) {
      // The reactivity should automatically update the UI since we're using props.itemBlock.isRequired directly
      // This watcher is mainly for debugging and ensuring we catch the changes
    }
  },
  { immediate: true },
);

// Expose save functions for child components to use (blur-based)
const triggerQuestionSave = async (questionId: number, content: string) => {
  await performQuestionSave(questionId, content);
};

const triggerOptionSave = async (
  optionId: number,
  field: 'optionText' | 'value',
  value: string | number,
) => {
  await performOptionSave(optionId, field, value);
};

// Expose option image save function for child components
const triggerOptionImageSave = async (optionId: number, imagePath: string) => {
  await performOptionImageSave(optionId, imagePath);
};

// Provide save functions to child components via provide/inject
provide('autoSave', {
  triggerQuestionAutoSave: triggerQuestionSave,
  triggerOptionAutoSave: triggerOptionSave,
  triggerOptionImageSave: triggerOptionImageSave,
  handleOptionImageUploaded: handleOptionImageUploaded,
  isSaving,
});

// Update function for question text content (real-time updates without saving)
function updateQuestionText(content: string) {
  headerText.value = content;
  // No auto-save here - save only on blur
}

// Update ItemBlock type via API
async function updateItemBlockType(newType: ItemBlockType) {
  try {
    if (!props.itemBlock.id) {
      console.error('Cannot update ItemBlock type: Missing block ID');
      return;
    }

    isUpdatingType.value = true;
    globalStore.startSaveOperation('Updating type...');
    // Update block type in backend
    const updatedBlock = await assessmentService.updateBlock({
      id: props.itemBlock.id,
      type: newType, // ✅ ส่ง type ใหม่ที่เลือกจริง
      isRequired: props.itemBlock.isRequired,
      assessmentId: props.itemBlock.assessmentId,
    });

    if (updatedBlock) {
      // Delete all existing options in the backend
      if (Array.isArray(updatedBlock.options) && updatedBlock.options.length > 0) {
        const { OptionService } = await import('src/services/asm/optionService');
        const optionService = new OptionService();
        await Promise.all(
          updatedBlock.options.map(async (opt) => {
            if (opt.id) {
              try {
                await optionService.removeOption(opt.id);
              } catch (e) {
                console.warn('Failed to delete option', opt.id, e);
              }
            }
          }),
        );

        await new Promise((resolve) => setTimeout(resolve, 50));
      }
      // Create a new default option for the new type
      const { OptionService } = await import('src/services/asm/optionService');
      const optionService = new OptionService();
      if (newType === 'RADIO' || newType === 'CHECKBOX') {
        await optionService.createOption({
          optionText: '',
          itemBlockId: updatedBlock.id,
          value: 0,
          sequence: 1,
        });
      } else if (newType === 'GRID') {
        await optionService.createOption({
          optionText: '',
          itemBlockId: updatedBlock.id,
          value: 0,
          sequence: 1,
        });
      }
      // Fetch the latest ItemBlock from backend to ensure UI is in sync
      const { ItemBlockService } = await import('src/services/asm/itemBlockService');
      const itemBlockService = new ItemBlockService();
      const latestBlock = await itemBlockService.getOne(updatedBlock.id);
      // Complete save operation successfully
      globalStore.completeSaveOperation(true, 'Type updated successfully');
      // Emit the latest block to parent for immediate UI update
      emit('update:question', {
        itemBlockId: props.itemBlock.id,
        updatedBlock: latestBlock || updatedBlock,
        typeChanged: true,
      });
      selectedBlockBody.value = extractBlockBodyType(updatedBlock) || blockBodyOptions[0];
    }
  } catch (error) {
    console.error('❌ ItemBlock type update failed:', error);
    globalStore.completeSaveOperation(false, 'Type update failed');
  } finally {
    isUpdatingType.value = false;
  }
}

// Handle option created event from child components
function handleOptionCreated(newOption: Option) {
  console.log('🎯 [ITEM-BLOCK] Option created event received:', {
    optionId: newOption.id,
    optionText: newOption.optionText,
    itemBlockId: props.itemBlock.id,
    currentOptionsCount: props.itemBlock.options?.length || 0,
  });

  // Emit to parent component to update the assessment store
  emit('update:option', {
    action: 'created',
    itemBlockId: props.itemBlock.id,
    option: newOption,
  });
}

// Handle option updated event from child components - FIXED for image upload refresh
function handleOptionUpdatedWithRefresh(
  optionId: number,
  updateData: { index: number; option: Option; action?: string },
) {
  console.log('🎯 [ITEM-BLOCK] Option updated event received:', {
    optionId,
    action: updateData.action,
    optionText: updateData.option?.optionText,
    itemBlockId: props.itemBlock.id,
    currentOptionsCount: props.itemBlock.options?.length || 0,
  });

  // ✅ FIX: Check if this is an image upload refresh request
  if (updateData.action === 'image-uploaded-refresh') {
    console.log('🔄 [ITEM-BLOCK] Triggering assessment refresh for image upload');
    // Refresh the entire assessment to show the uploaded image
    emit('refresh-assessment', { focusBlockId: props.itemBlock.id });
    return;
  }

  // Emit to parent component to update the assessment store
  emit('update:option', {
    action: 'updated',
    itemBlockId: props.itemBlock.id,
    optionId: optionId,
    updateData: updateData,
  });
}

// UNUSED - replaced by handleOptionUpdatedWithRefresh
/*
function handleOptionUpdated(
  optionId: number,
  updateData: { index: number; option: Option; action?: string },
) {
  // อัปเดต localOptions ทันที (ให้ทันเห็นผลเมื่อกด “เฉลยคำตอบ”)
  const index = localOptions.value.findIndex((opt) => opt.id === optionId);
  if (index !== -1) {
    localOptions.value[index] = updateData.option;
    localOptions.value = [...localOptions.value]; // บังคับ reactive
  }

  // ✅ sync กับ store (blockList และ activeAssessment)
  blockCreatorStore.updateOptionTextInStore(optionId, updateData.option.optionText);

  // 🔁 ส่ง emit ต่อไป (เผื่อ parent ใช้)
  emit('update:option', {
    action: 'updated',
    itemBlockId: props.itemBlock.id,
    optionId,
    updateData,
  });
}
*/

// Handle GRID question updates from GridBody component
function handleGridQuestionUpdate(updateData: {
  questionId?: number;
  questionText?: string;
  itemBlockId: number;
  updatedQuestion?: object;
  action: 'created' | 'updated' | 'deleted';
}) {
  // Forward to parent component with proper structure
  emit('update:question', {
    questionId: updateData.questionId,
    questionText: updateData.questionText,
    itemBlockId: updateData.itemBlockId,
    updatedQuestion: updateData.updatedQuestion,
    gridAction: updateData.action, // Add grid-specific action
  });
}

// Handle GRID option updates from GridBody component
function handleGridOptionUpdate(updateData: {
  action: 'created' | 'updated' | 'deleted';
  itemBlockId: number;
  option?: Option;
  optionId?: number;
  updateData?: { index: number; option: Option };
}) {
  // Forward to parent component
  emit('update:option', {
    action: updateData.action,
    itemBlockId: updateData.itemBlockId,
    option: updateData.option,
    optionId: updateData.optionId,
    updateData: updateData.updateData,
    isGridOption: true, // Flag to identify GRID options
  });
}

// Filter block body options based on type - exclude GRID for quiz type
const filteredBlockBodyOptions = computed(() => {
  if (props.type === 'quiz') {
    return blockBodyOptions.filter((option) => option.value !== 'GRID');
  }
  return blockBodyOptions;
});

const selectedBlockBody = ref<BlockBodyOptionsType>(
  extractBlockBodyType(props.itemBlock) || filteredBlockBodyOptions.value[0]!,
);

// Watch for changes in itemBlock type and update selectedBlockBody accordingly
watch(
  () => props.itemBlock.type,
  (newType) => {
    const newBlockBodyType = extractBlockBodyType(props.itemBlock);
    if (newBlockBodyType && selectedBlockBody.value.value !== newType) {
      selectedBlockBody.value = newBlockBodyType;
    }
  },
  { immediate: false },
);

const onBlockBodyChange = async (value: BlockBodyOptionsType) => {
  selectedBlockBody.value = value;

  // Update the ItemBlock type via API
  await updateItemBlockType(value.value);

  // Emit the change to parent component
  emit('update:type', value);
};

// Initialize component when mounted
onMounted(() => {
  // Set initial image if exists
  if (props.itemBlock.questions?.[0]?.imagePath) {
    uploadedImagePath.value = props.itemBlock.questions[0].imagePath;
    if (props.itemBlock.questions[0].imageWidth && props.itemBlock.questions[0].imageHeight) {
      imageWidth.value = props.itemBlock.questions[0].imageWidth;
      imageHeight.value = props.itemBlock.questions[0].imageHeight;
    }
  }
});

const onClickDuplicateItem = () => {
  // Emit duplicate event to parent component (same as three-dot menu)
  emit('duplicate');
};

const onClickDeleteItem = () => {
  // * implement deletion logic here
  emit('delete');
};

// Handle isRequired toggle - frontend only implementation
const handleIsRequiredToggle = (newValue: boolean) => {
  // Ensure we always work with a proper boolean value
  const booleanValue = Boolean(newValue);

  // Update the itemBlock's isRequired property directly (frontend only)
  itemBlock.value.isRequired = booleanValue;

  // Emit the update to parent components for state management
  emit('update:isRequired', {
    itemBlockId: itemBlock.value.id,
    isRequired: booleanValue,
  });
};

// Menu handler functions
function toggleMenu() {
  showMenu.value = !showMenu.value;
}

function onDuplicate() {
  emit('duplicate');
}

function onDelete() {
  emit('delete');
}

const currentComponent = computed<Component>(() => {
  if (!selectedBlockBody.value) {
    return OptionBody; // Default component if none selected
  }
  type ComponentType = Record<DropdownItemBlockType, Component>;

  const components: ComponentType = {
    RADIO: OptionBody,
    TEXTFIELD: TextBody,
    CHECKBOX: CheckBoxBody,
    GRID: GridBody,
    UPLOAD: FileUploadBody,
  };

  return components[selectedBlockBody.value.value];
});

// Handle card click with debouncing to prevent conflicts
let cardClickTimeout: NodeJS.Timeout | null = null;
function handleCardClick(event: Event) {
  // Clear any existing timeout
  if (cardClickTimeout) {
    clearTimeout(cardClickTimeout);
  }

  // Debounce the focus-fab emission to prevent conflicts with rapid events
  cardClickTimeout = setTimeout(() => {
    // Only emit focus-fab if the click wasn't on a button or interactive element
    const target = event.target as HTMLElement;
    if (!target.closest('button') && !target.closest('.q-btn') && !target.closest('.q-menu')) {
      emit('focus-fab');
    }
  }, 100);
}

// radio
watch(selectedAnswer, async (newSelectedId) => {
  if (props.type !== 'quiz' || newSelectedId == null) return;

  const options = localOptions.value;

  // for (const opt of options) {
  //   const isCorrect = opt.id === newSelectedId;
  //   if ((opt.value === 1) !== isCorrect) {
  //     await performOptionSave(opt.id, 'value', {
  //       optionText: opt.optionText,
  //       value: isCorrect ? 1 : 0,
  //     });
  //   }
  // }
  for (let i = 0; i < options.length; i++) {
    const opt = options[i];
    if (!opt) continue; // ✅ ป้องกัน undefined

    const isCorrect = opt.id === newSelectedId;
    if ((opt.value === 1) !== isCorrect) {
      options[i] = {
        ...opt,
        value: isCorrect ? 1 : 0,
      };

      await performOptionSave(opt.id, 'value', isCorrect ? 1 : 0);

      emit('update:option', {
        action: 'updated',
        itemBlockId: props.itemBlock.id,
        optionId: opt.id,
        updateData: {
          index: i,
          option: options[i],
        },
      });

      // ✅ บรรทัดนี้บังคับให้ Vue รู้ว่า localOptions เปลี่ยน
      localOptions.value = [...localOptions.value];
    }
  }
});

// checkbox
watch(selectedAnswers, async (newSelectedIds) => {
  if (props.type !== 'quiz') return;

  const options = localOptions.value;
  for (let i = 0; i < options.length; i++) {
    const opt = options[i];
    if (!opt) continue;

    const isCorrect = newSelectedIds.includes(opt.id);
    if ((opt.value === 1) !== isCorrect) {
      options[i] = { ...opt, value: isCorrect ? 1 : 0 };
      await performOptionSave(opt.id, 'value', isCorrect ? 1 : 0);
      emit('update:option', {
        action: 'updated',
        itemBlockId: props.itemBlock.id,
        optionId: opt.id,
        updateData: { index: i, option: options[i] },
      });
      localOptions.value = [...localOptions.value];
    }
  }
});

const localOptions = ref<Option[]>([]);

watch(
  () => props.itemBlock.options,
  (newOptions) => {
    localOptions.value = newOptions ? [...newOptions] : [];
  },
  { immediate: true },
);

// watch(
//   () => props.itemBlock.options,
//   (options) => {
//     const selected = options?.find((opt) => opt.value === 1);
//     selectedAnswer.value = selected?.id ?? null;
//   },
//   { immediate: true },
// );

watch(
  () => props.itemBlock.options,
  (options) => {
    if (selectedBlockBody.value.value === 'CHECKBOX') {
      selectedAnswers.value = options?.filter((o) => o.value === 1).map((o) => o.id) ?? [];
    } else {
      const selected = options?.find((o) => o.value === 1);
      selectedAnswer.value = selected?.id ?? null;
    }
  },
  { immediate: true },
);

async function handleImageUploaded() {
  if (!isImageUploadAllowed.value) return; // Block upload for disallowed types

  const questionId = props.itemBlock.questions?.[0]?.id;
  if (questionId) {
    try {
      // Since the UploadImage component already updated the question via AssessmentService.updateQuestion,
      // we need to refresh the itemBlock data to get the latest image information
      if (props.itemBlock.assessmentId) {
        // const updatedAssessment = await assessmentService.fetchOne(props.itemBlock.assessmentId);

        // Find the updated itemBlock in the assessment
        const { pagedItemBlocks } = await assessmentService.fetchOne(props.itemBlock.assessmentId);

        const updatedItemBlock = pagedItemBlocks.find((block) => block.id === props.itemBlock.id);

        if (updatedItemBlock && updatedItemBlock.questions?.[0]) {
          const updatedQuestion = updatedItemBlock.questions[0];

          // Update local reactive values with the fetched data
          if (updatedQuestion.imagePath) {
            uploadedImagePath.value = updatedQuestion.imagePath;
          }

          if (updatedQuestion.imageWidth && updatedQuestion.imageHeight) {
            imageWidth.value = updatedQuestion.imageWidth;
            imageHeight.value = updatedQuestion.imageHeight;
          }
        } else {
          console.warn('⚠️ Updated question not found in assessment data');
        }
      }
    } catch (error) {
      console.error('❌ Failed to refresh assessment data:', error);
    }
  } else {
    // For regular ItemBlock images, the UploadImage component handles the ImageBody update
    // We might need to refresh the ImageBody data here if needed
    if (props.itemBlock.id) {
      try {
        // Import ImageBodyService dynamically to avoid circular dependencies
        const { ImageBodyService } = await import('src/services/asm/imageBodyService');
        const imageBodyService = new ImageBodyService();

        const imageBody = await imageBodyService.getImageBodyByItemBlockId(props.itemBlock.id);

        if (imageBody && imageBody.imagePath) {
          uploadedImagePath.value = imageBody.imagePath;

          if (imageBody.imageWidth && imageBody.imageHeight) {
            imageWidth.value = imageBody.imageWidth;
            imageHeight.value = imageBody.imageHeight;
          }
        }
      } catch (error) {
        console.error('❌ Failed to fetch updated ImageBody data:', error);
      }
    }
  }
}

// Handler for option image uploads - called by child components
async function handleOptionImageUploaded(optionId: number, imagePath: string) {
  try {
    // Call the option image save function
    await performOptionImageSave(optionId, imagePath);

    // Refresh the assessment data to get the latest option information
    if (props.itemBlock.assessmentId) {
      // const updatedAssessment = await assessmentService.fetchOne(props.itemBlock.assessmentId);

      // Find the updated itemBlock in the assessment
      // const updatedItemBlock = updatedAssessment.itemBlocks?.find(
      //   (block) => block.id === props.itemBlock.id,
      // );

      const { pagedItemBlocks } = await assessmentService.fetchOne(props.itemBlock.assessmentId);
      const updatedItemBlock = pagedItemBlocks.find((block) => block.id === props.itemBlock.id);

      if (updatedItemBlock) {
        // Emit refresh to parent to update the entire assessment state
        emit('refresh-assessment', { focusBlockId: props.itemBlock.id });
      }
    }
  } catch (error) {
    console.error('❌ Failed to handle option image upload:', error);
  }
}

function onImageBtnClick() {
  if (!isImageUploadAllowed.value) return; // Block upload for disallowed types
  // Always use the latest questionId
  const questionId = props.itemBlock.questions?.[0]?.id;
  if (questionId) {
    void assessmentService.updateQuestion(questionId, {}).then(() => {
      showImageDialog.value = true;
    });
  } else {
    showImageDialog.value = true;
  }
}

const imageStyle = computed(() => {
  if (imageWidth.value && imageHeight.value) {
    return `max-width:100%; height:auto; width:${imageWidth.value}px; height:${imageHeight.value}px; display:block; margin:0 auto;`;
  }
  return 'max-width:300px; max-height:200px; display:block; margin:0 auto;';
});

function onImageLoad(event: Event) {
  const img = event.target as HTMLImageElement;
  imageNaturalWidth.value = img.naturalWidth;
  imageNaturalHeight.value = img.naturalHeight;
  if (!imageWidth.value || !imageHeight.value) {
    imageWidth.value = img.naturalWidth;
    imageHeight.value = img.naturalHeight;
  }
}

async function onImageResize({ width, height }: { width: number; height: number }) {
  // Update local reactive values immediately for responsive UI
  imageWidth.value = width;
  imageHeight.value = height;

  // Save the updated dimensions to the backend via PATCH /questions/{id} API
  const questionId = props.itemBlock.questions?.[0]?.id;
  if (questionId) {
    await performQuestionImageDimensionsSave(questionId, width, height);
  }
}

async function saveScoreAuto() {
  if (props.type !== 'quiz') return;
  const firstQuestion = props.itemBlock.questions?.[0];
  if (!firstQuestion?.id) return;

  try {
    isSaving.value = true;
    globalStore.startSaveOperation('Saving score...');
    const updatedQuestion = await assessmentService.updateQuestion(firstQuestion.id, {
      score: number.value,
      itemBlockId: props.itemBlock.id,
    });

    if (updatedQuestion) {
      globalStore.completeSaveOperation(true, 'Score saved');
      emit('update:question', {
        questionId: firstQuestion.id,
        score: updatedQuestion.score,
        itemBlockId: props.itemBlock.id,
        updatedQuestion,
      });
    }
  } catch (e) {
    globalStore.completeSaveOperation(false, 'Save score failed');
    console.error('Save score failed:', e);
  } finally {
    isSaving.value = false;
  }
}

watch(number, (val, oldVal) => {
  if (val !== oldVal) {
    void saveScoreAuto();
  }
});

function isSelected(id: string | number) {
  return selectedBlockBody?.value.value === 'CHECKBOX'
    ? selectedAnswers.value.includes(id)
    : selectedAnswer.value === id;
}

function onSelect(id: string | number) {
  if (selectedBlockBody?.value.value === 'CHECKBOX') {
    const index = selectedAnswers.value.indexOf(id);
    if (index === -1) selectedAnswers.value.push(id);
    else selectedAnswers.value.splice(index, 1);
  } else {
    selectedAnswer.value = id;
  }
}

// ...existing code...
</script>

<style scoped>
.q-select {
  box-sizing: border-box;
  width: 255px;
  background: #fffdfd;
  border: 1px solid #b1b1b1;
  border-radius: 10px;
}

.main-question-input {
  transition: all 0.3s ease;
}

.item-block-container {
  position: relative;
}

.item-top-bar {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: -16px;
  margin-bottom: 8px;
}

.three-dot-menu {
  background-color: transparent;
  border: none;
  border-radius: 0;
  padding: 4px;
  cursor: grab;
  outline: none;
}

.three-dot-menu:hover {
  background-color: transparent;
}

.three-dot-menu:active {
  cursor: grabbing;
}

.three-dot-menu:focus {
  outline: none;
  box-shadow: none;
}

.question-top-bar {
  padding: 8px 16px;
  border-bottom: 1px solid #f0f0f0;
  background-color: #fafafa;
  border-radius: 8px 8px 0 0;
  margin-bottom: 8px;
}

.save-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(25, 118, 210, 0.1);
  border: 1px solid rgba(25, 118, 210, 0.2);
  border-radius: 8px;
  padding: 4px 8px;
  font-size: 0.75rem;
}

.save-text {
  color: #1976d2;
  font-weight: 500;
}
</style>
